#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的图片路径
"""

from db_config import get_db_connection
import os

def check_image_paths():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询MSC20250802001的问题点记录
        cursor.execute("""
            SELECT msq.question_number, msq.question_text, msq.image_path, mscf.report_code
            FROM material_sample_questions msq
            JOIN material_sample_confirmation_form mscf ON msq.form_id = mscf.id
            WHERE mscf.report_code = 'MSC20250802001'
            ORDER BY msq.question_number
        """)
        
        records = cursor.fetchall()
        print('=== MSC20250802001 的图片路径记录 ===')
        for record in records:
            print(f'问题点: {record["question_number"]}')
            print(f'问题文本: {record["question_text"]}')
            print(f'图片路径: {record["image_path"]}')
            
            # 检查文件是否存在
            image_path = record["image_path"]
            if image_path:
                exists = os.path.exists(image_path)
                print(f'文件存在: {exists}')
            else:
                print('无图片路径')
            print('-' * 50)
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'查询失败: {e}')

if __name__ == "__main__":
    check_image_paths()
