#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查图片文件是否存在
"""

import os
import glob

def check_image_files():
    # 检查最终图片目录
    final_dir = r"D:\检验系统图片\2025\08\MSC20250802001"
    print(f"=== 检查最终图片目录: {final_dir} ===")
    
    if os.path.exists(final_dir):
        files = os.listdir(final_dir)
        print(f"目录存在，包含 {len(files)} 个文件:")
        for file in files:
            file_path = os.path.join(final_dir, file)
            size = os.path.getsize(file_path)
            print(f"  - {file} ({size} bytes)")
    else:
        print("目录不存在")
    
    # 检查临时图片目录
    temp_base = r"D:\检验系统图片\temp_uploads"
    print(f"\n=== 检查临时图片目录: {temp_base} ===")
    
    if os.path.exists(temp_base):
        temp_dirs = [d for d in os.listdir(temp_base) if os.path.isdir(os.path.join(temp_base, d))]
        print(f"临时目录存在，包含 {len(temp_dirs)} 个子目录:")
        
        for temp_dir in temp_dirs:
            temp_path = os.path.join(temp_base, temp_dir)
            files = os.listdir(temp_path)
            print(f"  - {temp_dir}: {len(files)} 个文件")
            for file in files:
                if file.startswith('8_'):  # 问题点8的图片
                    file_path = os.path.join(temp_path, file)
                    size = os.path.getsize(file_path)
                    print(f"    * {file} ({size} bytes)")
    else:
        print("临时目录不存在")
    
    # 搜索所有问题点8的图片
    print(f"\n=== 搜索所有问题点8的图片 ===")
    search_patterns = [
        r"D:\检验系统图片\**\8_*.png",
        r"D:\检验系统图片\**\8_*.jpg",
        r"D:\检验系统图片\**\8_*.jpeg"
    ]
    
    for pattern in search_patterns:
        files = glob.glob(pattern, recursive=True)
        if files:
            print(f"找到 {len(files)} 个匹配文件 ({pattern}):")
            for file in files:
                size = os.path.getsize(file)
                print(f"  - {file} ({size} bytes)")

if __name__ == "__main__":
    check_image_files()
