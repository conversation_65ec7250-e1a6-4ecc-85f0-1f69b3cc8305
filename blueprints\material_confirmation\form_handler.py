from flask import Blueprint, render_template, request, jsonify, send_file, abort
import mysql.connector, json
import os
from werkzeug.utils import secure_filename  # 用于安全文件名处理
from db_config import get_db_connection
from datetime import datetime
import time, uuid
import shutil
from apscheduler.schedulers.background import BackgroundScheduler
from threading import Lock
import random  # 添加random模块

# 创建蓝图
Material_Sample_Confirmation_Form_bp = Blueprint(
    'Material_Sample_Confirmation_Form', 
    __name__,
    url_prefix='/Material_Sample_Confirmation_Form'  # 添加这一行
)

# 定义全局锁
file_upload_lock = Lock()


"""获取图片存储基础路径
    返回格式：绝对路径字符串
    路径组成逻辑：
    1. 基于当前文件路径向上回溯两级目录（到项目根目录）
    2. 定位到 static/images/Material_Sample 目录
    3. 自动创建缺失目录（需确保运行环境有写入权限）
"""
# 获取图片存储基础路径
import threading

# 定义全局锁
directory_creation_lock = threading.Lock()

# 定义全局变量存储物料料号与UUID的映射
current_material_uuid = {}

def get_image_base_path(report_code=None):
    with directory_creation_lock:
        """
        获取图片存储的基础路径。
        如果提供了 report_code，则返回完整的存储路径（包含年份、月份和报告编码）。
        """
        try:
            # 读取配置文件
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'settings.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    base_path = config.get('image_base_path', 'D:/检验系统图片/')
            else:
                base_path = 'D:/检验系统图片/'  # 默认路径
            
            # 确保基础路径存在
            os.makedirs(base_path, exist_ok=True)
            
            if not os.path.exists(base_path):
                raise Exception(f"目录创建失败，请检查权限: {base_path}")
            
            if report_code:
                year = datetime.now().strftime("%Y")
                month = datetime.now().strftime("%m")
                
                year_path = os.path.join(base_path, year)
                month_path = os.path.join(year_path, month)
                
                try:
                    os.makedirs(year_path, exist_ok=True)
                    os.makedirs(month_path, exist_ok=True)
                except Exception as e:
                    print(f"Failed to create year/month directories: {e}")
                    raise
                
                final_path = os.path.join(month_path, report_code)
                try:
                    if not os.path.exists(final_path):
                        os.makedirs(final_path, exist_ok=True)
                except Exception as e:
                    print(f"Failed to create final report directory: {e}")
                    raise
                
                return os.path.abspath(final_path)
            else:
                return os.path.abspath(base_path)
                
        except Exception as e:
            print(f"Error in get_image_base_path: {e}")
            raise Exception(f"无法创建或访问图片存储路径: {str(e)}")

def clean_temp_files():
    temp_base = os.path.normpath(os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        '../../static/images/temp_uploads'
    ))
    now = time.time()

    for dir_name in os.listdir(temp_base):
        dir_path = os.path.join(temp_base, dir_name)
        # 检查目录最后修改时间
        if os.stat(dir_path).st_mtime < now - 86400:  # 超过24小时
            shutil.rmtree(dir_path)
            print(f"Deleted expired directory: {dir_path}")

            # 清理对应的UUID映射
            for material_number, unique_id in list(current_material_uuid.items()):
                if f"{material_number}_" in dir_name:
                    del current_material_uuid[material_number]
                    print(f"Cleared UUID mapping for material number: {material_number}")

# 初始化调度器
scheduler = BackgroundScheduler()
scheduler.add_job(clean_temp_files, 'cron', hour=0, minute=0)
scheduler.start()

# 测试路径生成的方法
@Material_Sample_Confirmation_Form_bp.route('/test_create_directory', methods=['GET'])
def test_create_directory():
    """
    测试路径生成和文件夹创建的接口。
    """
    try:
        # 调用 get_image_base_path 方法生成路径
        base_path = get_image_base_path()
        return jsonify({
            'status': 'success',
            'message': f'Directory created successfully: {base_path}'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


# 路由：渲染物料样板确认书页面
@Material_Sample_Confirmation_Form_bp.route('/', methods=['GET'])
def material_sample_confirmation_form():
    from flask import session, redirect, url_for, flash

    # 检查是否从正确的入口访问
    if not session.get('material_confirmation_access'):
        # 如果不是从物料确认菜单进入，重定向到确认清单页面
        flash('请通过物料确认菜单进入新增确认检验功能', 'warning')
        return redirect(url_for('material_confirmation.index'))

    # 清除session标记（一次性使用）
    session.pop('material_confirmation_access', None)

    return render_template('material_confirmation/Material_Sample_Confirmation_Form.html')

# 路由：编辑物料样板确认书页面
@Material_Sample_Confirmation_Form_bp.route('/edit/<report_code>', methods=['GET'])
def edit_material_sample_confirmation_form(report_code):
    """编辑物料样板确认书"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 查询基本信息
        cursor.execute("""
            SELECT report_code, supplier, inspection_date, sample_count, inspector,
                   material_number, graph_number, material_name, drawing_version,
                   material_texture, surface_processing, sample_status, other_textbox,
                   final_judgment, opinion, review
            FROM material_sample_confirmation_form
            WHERE report_code = %s
        """, (report_code,))
        base_info = cursor.fetchone()

        if not base_info:
            return f"未找到报告编码为 {report_code} 的记录", 404

        # 查询尺寸数据
        cursor.execute("""
            SELECT
                size_number,
                COALESCE(position, '') as position,
                COALESCE(value, '') as value,
                COALESCE(min_value, '') as min_value,
                COALESCE(max_value, '') as max_value,
                COALESCE(measure_1, '') as measure_1,
                COALESCE(measure_2, '') as measure_2,
                COALESCE(measure_3, '') as measure_3,
                COALESCE(measure_4, '') as measure_4,
                COALESCE(measure_5, '') as measure_5,
                COALESCE(NULLIF(check_result, ''), '/') as check_result,
                COALESCE(note, '') as note
            FROM material_sample_size_data
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY size_number
        """, (report_code,))
        size_data = cursor.fetchall()

        # 查询外观数据
        cursor.execute("""
            SELECT
                check_number,
                COALESCE(NULLIF(check_result, ''), '/') as check_result,
                COALESCE(note, '') as note,
                COALESCE(other_info, '') as other_info
            FROM material_sample_appearance
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY check_number
        """, (report_code,))
        appearance_data = cursor.fetchall()

        # 查询功能数据
        cursor.execute("""
            SELECT
                check_number,
                COALESCE(NULLIF(check_result, ''), '/') as check_result,
                COALESCE(note, '') as note,
                COALESCE(burnin_info, '') as burnin_info,
                COALESCE(electrical_info, '') as electrical_info,
                COALESCE(tests_info, '') as tests_info,
                COALESCE(other_test, '') as other_test,
                COALESCE(other_info, '') as other_info
            FROM material_sample_function
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY check_number
        """, (report_code,))
        function_data = cursor.fetchall()

        # 查询问题点数据
        cursor.execute("""
            SELECT
                question_number,
                COALESCE(question_text, '') as question_text,
                COALESCE(image_path, '') as image_path
            FROM material_sample_questions
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY question_number
        """, (report_code,))
        questions_data = cursor.fetchall()

        # 处理图片路径，转换为Web可访问的路径
        for question in questions_data:
            if question['image_path']:
                image_path = question['image_path']

                # 获取图片基础路径
                base_path = get_image_base_path()

                try:
                    # 检查是否已经是Web路径格式
                    if image_path.startswith('/Material_Sample_Confirmation_Form/image/'):
                        # 已经是Web路径，直接使用
                        question['web_image_path'] = image_path
                    elif os.path.isabs(image_path):
                        # 绝对路径，转换为相对路径再构建Web路径
                        rel_path = os.path.relpath(image_path, base_path)
                        web_path = f'/Material_Sample_Confirmation_Form/image/{rel_path.replace(os.sep, "/")}'
                        question['web_image_path'] = web_path
                    else:
                        # 相对路径，直接构建Web路径
                        web_path = f'/Material_Sample_Confirmation_Form/image/{image_path.replace(os.sep, "/")}'
                        question['web_image_path'] = web_path

                    print(f"图片路径转换: {image_path} -> {question['web_image_path']}")

                except Exception as e:
                    print(f"图片路径转换失败: {e}")
                    # 如果转换失败，尝试直接使用文件名
                    filename = os.path.basename(image_path)
                    question['web_image_path'] = f'/Material_Sample_Confirmation_Form/image/{filename}'
            else:
                question['web_image_path'] = ''

        cursor.close()
        conn.close()

        # 渲染编辑页面，传入所有数据
        return render_template(
            'material_confirmation/Material_Sample_Confirmation_Form.html',
            # 基本信息
            report_code=base_info['report_code'],
            supplier=base_info['supplier'],
            inspection_date=base_info['inspection_date'],
            sample_count=base_info['sample_count'] if base_info['sample_count'] is not None else '',
            inspector=base_info['inspector'],
            material_number=base_info['material_number'],
            graph_number=base_info['graph_number'],
            material_name=base_info['material_name'],
            drawing_version=base_info['drawing_version'],
            material_texture=base_info['material_texture'],
            surface_processing=base_info['surface_processing'],
            sample_status_list=base_info['sample_status'].split(',') if base_info['sample_status'] else [],
            other_textbox_value=base_info['other_textbox'],
            final_judgment=base_info['final_judgment'],
            opinion=base_info['opinion'],
            review=base_info['review'],
            # 数据
            size_data=size_data,
            appearance_data=appearance_data,
            function_data=function_data,
            questions_data=questions_data,
            # 编辑模式标记
            edit_mode=True
        )

    except Exception as e:
        return f"加载编辑页面失败: {str(e)}", 500

# 路由：提供图片服务
@Material_Sample_Confirmation_Form_bp.route('/image/<path:image_path>')
def serve_image(image_path):
    """提供图片文件服务"""
    try:
        # 获取图片基础路径
        base_path = get_image_base_path()

        # 构建完整的图片路径
        full_path = os.path.join(base_path, image_path)

        # 安全检查：确保路径在允许的目录内
        if not os.path.abspath(full_path).startswith(os.path.abspath(base_path)):
            abort(403)  # 禁止访问

        # 检查文件是否存在
        if not os.path.exists(full_path):
            abort(404)  # 文件不存在

        # 返回文件
        return send_file(full_path)

    except Exception as e:
        print(f"图片服务错误: {e}")
        abort(500)

# 路由：处理表单更新数据
@Material_Sample_Confirmation_Form_bp.route('/update_material_sample/<report_code>', methods=['POST'])
def update_material_sample(report_code):
    """更新物料样板确认书数据"""
    try:
        print(f"[DEBUG] 开始更新物料样板确认书，报告编码: {report_code}")

        # 获取表单数据
        data = request.get_json()
        print(f"[DEBUG] 接收到的数据类型: {type(data)}")

        if data is None:
            print("[ERROR] 未接收到JSON数据")
            return jsonify({
                'status': 'error',
                'message': '未接收到有效的JSON数据'
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 更新基本信息
        cursor.execute("""
            UPDATE material_sample_confirmation_form
            SET supplier = %s, inspection_date = %s, sample_count = %s, inspector = %s,
                material_number = %s, graph_number = %s, material_name = %s,
                drawing_version = %s, material_texture = %s, surface_processing = %s,
                sample_status = %s, other_textbox = %s, final_judgment = %s,
                opinion = %s, review = %s
            WHERE report_code = %s
        """, (
            data.get('supplier', ''),
            data.get('inspection_date', ''),
            data.get('sample_count', ''),
            data.get('inspector', ''),
            data.get('material_number', ''),
            data.get('graph_number', ''),
            data.get('material_name', ''),
            data.get('drawing_version', ''),
            data.get('material_texture', ''),
            data.get('surface_processing', ''),
            data.get('sample_status', ''),
            data.get('other_textbox', ''),
            data.get('final_judgment', ''),
            data.get('opinion', ''),
            data.get('review', ''),
            report_code
        ))

        # 获取表单ID
        cursor.execute("SELECT id FROM material_sample_confirmation_form WHERE report_code = %s", (report_code,))
        form_result = cursor.fetchone()
        if not form_result:
            return jsonify({'status': 'error', 'message': '未找到对应的表单记录'}), 404

        form_id = form_result[0]

        # 更新尺寸数据
        if 'size_data' in data:
            # 先删除现有数据
            cursor.execute("DELETE FROM material_sample_size_data WHERE form_id = %s", (form_id,))

            # 插入新数据
            for size_item in data['size_data']:
                cursor.execute("""
                    INSERT INTO material_sample_size_data
                    (form_id, size_number, position, value, min_value, max_value,
                     measure_1, measure_2, measure_3, measure_4, measure_5, check_result, note)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    form_id, size_item.get('size_number', 0),
                    size_item.get('position', ''), size_item.get('value', ''),
                    size_item.get('min_value', ''), size_item.get('max_value', ''),
                    size_item.get('measure_1', ''), size_item.get('measure_2', ''),
                    size_item.get('measure_3', ''), size_item.get('measure_4', ''),
                    size_item.get('measure_5', ''), size_item.get('check_result', ''),
                    size_item.get('note', '')
                ))

        # 更新外观数据
        if 'appearance_data' in data:
            cursor.execute("DELETE FROM material_sample_appearance WHERE form_id = %s", (form_id,))
            for appearance_item in data['appearance_data']:
                cursor.execute("""
                    INSERT INTO material_sample_appearance
                    (form_id, check_number, check_result, note, other_info)
                    VALUES (%s, %s, %s, %s, %s)
                """, (
                    form_id, appearance_item.get('check_number', 0),
                    appearance_item.get('check_result', ''), appearance_item.get('note', ''),
                    appearance_item.get('other_info', '')
                ))

        # 更新功能数据
        if 'function_data' in data:
            cursor.execute("DELETE FROM material_sample_function WHERE form_id = %s", (form_id,))
            for function_item in data['function_data']:
                cursor.execute("""
                    INSERT INTO material_sample_function
                    (form_id, check_number, check_result, note, burnin_info, electrical_info, tests_info, other_test, other_info)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    form_id, function_item.get('check_number', 0),
                    function_item.get('check_result', ''), function_item.get('note', ''),
                    function_item.get('burnin_info', ''), function_item.get('electrical_info', ''),
                    function_item.get('tests_info', ''), function_item.get('other_test', ''),
                    function_item.get('other_info', '')
                ))

        # 更新问题点数据
        # 先删除现有的问题点数据
        cursor.execute("DELETE FROM material_sample_questions WHERE form_id = %s", (form_id,))

        # 处理问题点文本数据
        questions_data = {}
        for key, value in data.items():
            if key.startswith('question_') and key.replace('question_', '').isdigit():
                question_number = int(key.replace('question_', ''))
                if value.strip():  # 只保存非空的问题描述
                    questions_data[question_number] = value.strip()

        # 处理现有图片（编辑模式下保留的图片）
        existing_images = data.get('existing_images', [])
        for img_info in existing_images:
            question_number = int(img_info.get('question_number', 0))
            image_path = img_info.get('image_path', '')
            question_text = questions_data.get(question_number, '')

            if question_number > 0:
                cursor.execute("""
                    INSERT INTO material_sample_questions
                    (form_id, question_number, question_text, image_path)
                    VALUES (%s, %s, %s, %s)
                """, (form_id, question_number, question_text, image_path))

        # 处理新上传的图片
        temp_ids = data.get('temp_ids', [])
        print(f"[DEBUG] 接收到的temp_ids: {temp_ids}")

        if temp_ids:
            # 移动新上传的图片到正式目录
            print(f"[DEBUG] 开始移动图片，temp_ids: {temp_ids}, report_code: {report_code}")
            move_result = move_images(temp_ids, report_code, data.get('material_number', ''))
            print(f"[DEBUG] 图片移动结果: {move_result}")

            if move_result.get('success') and move_result.get('final_paths'):
                print(f"[DEBUG] 图片移动成功，开始更新数据库")
                # 更新数据库中的图片路径
                for temp_id, final_path in move_result['final_paths'].items():
                    # 从temp_id中提取question_number
                    # temp_id可能是数字字符串（如'1', '2'）或question格式（如'question1'）
                    question_number = None
                    if temp_id.isdigit():
                        # 直接是数字字符串
                        question_number = int(temp_id)
                    elif temp_id.startswith('question') and temp_id[8:].isdigit():
                        # question格式
                        question_number = int(temp_id[8:])

                    if question_number is not None:
                        question_text = questions_data.get(question_number, '')
                        print(f"[DEBUG] 处理问题点 {question_number}，图片路径: {final_path}")

                        # 将完整路径转换为相对路径存储到数据库
                        base_path = get_image_base_path()
                        try:
                            if os.path.isabs(final_path):
                                # 转换为相对于基础路径的相对路径
                                relative_path = os.path.relpath(final_path, base_path)
                                db_path = relative_path.replace(os.sep, "/")  # 统一使用正斜杠
                            else:
                                db_path = final_path.replace(os.sep, "/")
                            print(f"[DEBUG] 数据库存储路径: {db_path}")
                        except Exception as e:
                            print(f"[ERROR] 路径转换失败: {e}")
                            db_path = os.path.basename(final_path)  # 备用方案：只存储文件名

                        # 检查是否已经有这个问题点的记录
                        cursor.execute("""
                            SELECT id FROM material_sample_questions
                            WHERE form_id = %s AND question_number = %s
                        """, (form_id, question_number))
                        existing_record = cursor.fetchone()

                        if existing_record:
                            # 更新现有记录的图片路径
                            cursor.execute("""
                                UPDATE material_sample_questions
                                SET image_path = %s, question_text = %s
                                WHERE form_id = %s AND question_number = %s
                            """, (db_path, question_text, form_id, question_number))
                        else:
                            # 插入新记录
                            cursor.execute("""
                                INSERT INTO material_sample_questions
                                (form_id, question_number, question_text, image_path)
                                VALUES (%s, %s, %s, %s)
                            """, (form_id, question_number, question_text, db_path))
            else:
                print(f"[ERROR] 图片移动失败或没有final_paths: {move_result}")
        else:
            print(f"[DEBUG] 没有需要移动的图片，temp_ids为空")

        # 处理只有文本没有图片的问题点
        for question_number, question_text in questions_data.items():
            cursor.execute("""
                SELECT id FROM material_sample_questions
                WHERE form_id = %s AND question_number = %s
            """, (form_id, question_number))
            existing_record = cursor.fetchone()

            if not existing_record:
                # 插入只有文本的问题点记录
                cursor.execute("""
                    INSERT INTO material_sample_questions
                    (form_id, question_number, question_text, image_path)
                    VALUES (%s, %s, %s, %s)
                """, (form_id, question_number, question_text, ''))

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': '物料样板确认书更新成功',
            'report_code': report_code
        })

    except Exception as e:
        print(f"[ERROR] 更新物料样板确认书时发生异常: {str(e)}")
        print(f"[ERROR] 异常类型: {type(e).__name__}")
        import traceback
        print(f"[ERROR] 异常堆栈: {traceback.format_exc()}")

        return jsonify({
            'status': 'error',
            'message': f'更新失败: {str(e)}'
        }), 500

# 路由：处理表单提交数据（生成报告编码并保存数据）
@Material_Sample_Confirmation_Form_bp.route('/submit_material_sample', methods=['POST'])
def submit_material_sample():
    # 添加提交状态检查，防止重复提交
    if hasattr(request, '_submitted') and request._submitted:
        return jsonify({'status': 'error', 'message': '请求已提交，请勿重复操作'}), 400
    
    # 标记请求为已提交
    request._submitted = True
    
    conn = None
    cursor = None
    try:
        # 获取表单数据
        data = request.get_json()
        temp_ids = data.get('temp_ids', [])
        print(f"原始的 temp_ids: {temp_ids}, 类型: {type(temp_ids)}")
        
        if isinstance(temp_ids, str):
            try:
                # 尝试解析JSON字符串为列表
                temp_ids = json.loads(temp_ids)
                print(f"解析JSON后的 temp_ids: {temp_ids}")
            except json.JSONDecodeError:
                # 如果不是有效的JSON，则按逗号分割
                temp_ids = temp_ids.split(',')
                print(f"按逗号分割后的 temp_ids: {temp_ids}")

        # 过滤掉空字符串
        temp_ids = [tid for tid in temp_ids if tid]
        print(f"过滤后的 temp_ids: {temp_ids}")
        
        # 删除对temp_ids的必填验证，允许无图片提交
        if not temp_ids:
            print("提示: 没有图片需要处理")
            # 继续处理表单数据，而不是返回错误

        print("sample_status字段值:", data.get('sample_status'))  # 调试打印sample_status字段
        print("other_textbox字段值:", data.get('other_textbox'))  # 调试打印 other_textbox 字段
        
        # 获取当前时间戳
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        report_prefix = f"MSC{timestamp[:8]}"
        data['created_at'] = timestamp

        # 开启事务
        conn = get_db_connection()
        cursor = conn.cursor()
        conn.start_transaction()  # 显式开启事务
        
        try:
            # 查询当天最大序列号（带事务锁）
            cursor.execute(
                "SELECT MAX(CAST(SUBSTRING(report_code, 12) AS UNSIGNED)) FROM material_sample_confirmation_form WHERE report_code LIKE %s FOR UPDATE",
                (f"{report_prefix}%",)
            )
            max_sequence = cursor.fetchone()[0] or 0
            print(f"当前最大序列号: {max_sequence}")

            # 生成新的序列号（确保+1）
            new_code = f"{report_prefix}{max_sequence + 1:03d}"
            print(f"生成的新报告编码: {new_code}")

            # 如果有图片，则先处理图片上传
            image_paths = []
            if temp_ids:
                print(f"处理图片，temp_ids: {temp_ids}")
                confirm_images_response = move_images(
                    temp_ids=temp_ids,
                    report_code=new_code,
                    material_number=data['material_number']
                )
                if confirm_images_response.get('error'):
                    raise Exception(f"图片处理失败: {confirm_images_response['error']}")
                
                # 保存图片路径
                image_paths = confirm_images_response.get('final_paths', [])
            else:
                print("无图片需要处理")
                confirm_images_response = {"success": True, "moved_files": [], "final_paths": []}

            # 1. 首先插入基本信息
            base_fields = [
                'report_code', 'supplier', 'inspection_date', 'created_at', 'sample_count', 'inspector',
                'material_number', 'graph_number', 'material_name', 'drawing_version',
                'material_texture', 'surface_processing', 'sample_status', 'other_textbox',
                'final_judgment', 'opinion', 'review'
            ]
            
            print("准备插入基本信息...")
            
            # 处理数值类型字段
            def process_value(value, field_type='str'):
                if value is None or value == '':
                    return None
                if field_type == 'int':
                    try:
                        return int(value) if value else None
                    except (ValueError, TypeError):
                        return None
                if field_type == 'float':
                    try:
                        return float(value) if value else None
                    except (ValueError, TypeError):
                        return None
                return value
            
            # 处理基本字段值
            base_values = [new_code]  # report_code
            base_values.append(data.get('supplier'))  # supplier
            base_values.append(data.get('inspection_date'))  # inspection_date
            base_values.append(data.get('created_at'))  # created_at
            base_values.append(process_value(data.get('sample_count'), 'int'))  # sample_count
            base_values.extend([data.get(field) for field in base_fields[5:]])  # 其他字段
            
            print(f"基本信息字段: {base_fields}")
            print(f"基本信息值: {base_values}")
            
            base_query = "INSERT INTO material_sample_confirmation_form (" + ", ".join(base_fields) + ") VALUES (" + ", ".join(["%s"] * len(base_fields)) + ")"
            cursor.execute(base_query, base_values)
            form_id = cursor.lastrowid
            print(f"基本信息插入成功，form_id: {form_id}")

            # 2. 插入尺寸数据
            print("准备插入尺寸数据...")

            # 收集所有有效的尺寸数据行
            valid_size_data = []
            for i in range(1, 31):
                size_data = {
                    'form_id': form_id,
                    'size_number': i,
                    'position': data.get(f'size_{i}_position'),
                    'value': process_value(data.get(f'size_{i}_value'), 'float'),
                    'min_value': process_value(data.get(f'size_{i}_min'), 'float'),
                    'max_value': process_value(data.get(f'size_{i}_max'), 'float'),
                    'measure_1': process_value(data.get(f'size_{i}_measure_1'), 'float'),
                    'measure_2': process_value(data.get(f'size_{i}_measure_2'), 'float'),
                    'measure_3': process_value(data.get(f'size_{i}_measure_3'), 'float'),
                    'measure_4': process_value(data.get(f'size_{i}_measure_4'), 'float'),
                    'measure_5': process_value(data.get(f'size_{i}_measure_5'), 'float'),
                    'check_result': data.get(f'size_{i}_check'),
                    'note': data.get(f'size_{i}_note')
                }
                
                # 检查此行是否有任何有效数据
                has_data = any([
                    size_data['position'], 
                    size_data['value'], 
                    size_data['min_value'], 
                    size_data['max_value'],
                    size_data['measure_1'], 
                    size_data['measure_2'], 
                    size_data['measure_3'], 
                    size_data['measure_4'], 
                    size_data['measure_5'],
                    size_data['check_result'], 
                    size_data['note']
                ])
                
                if has_data:
                    valid_size_data.append(size_data)

            # 插入前3行数据，确保即使为空也插入
            for i in range(1, 4):
                size_fields = [
                    'position', 'value', 'min_value', 'max_value',
                    'measure_1', 'measure_2', 'measure_3', 'measure_4', 'measure_5',
                    'check_result', 'note'
                ]
                
                # 如果有有效数据，则使用；否则插入空行
                if i <= len(valid_size_data):
                    size_data = valid_size_data[i-1]
                else:
                    size_data = {
                        'form_id': form_id,
                        'size_number': i,
                        'position': None,
                        'value': None,
                        'min_value': None,
                        'max_value': None,
                        'measure_1': None,
                        'measure_2': None,
                        'measure_3': None,
                        'measure_4': None,
                        'measure_5': None,
                        'check_result': None,
                        'note': None
                    }
                
                # 更新行号为当前位置
                size_data['size_number'] = i
                
                print(f"插入尺寸数据 {i}: {size_data}")
                size_query = "INSERT INTO material_sample_size_data (" + ", ".join(['form_id', 'size_number'] + size_fields) + ") VALUES (" + ", ".join(["%s"] * (len(size_fields) + 2)) + ")"
                cursor.execute(size_query, [form_id, i] + [size_data[field] for field in size_fields])

            # 插入剩余的有效数据行（从第4行开始）
            for idx, size_data in enumerate(valid_size_data[3:], 4):
                size_fields = [
                    'position', 'value', 'min_value', 'max_value',
                    'measure_1', 'measure_2', 'measure_3', 'measure_4', 'measure_5',
                    'check_result', 'note'
                ]
                
                # 更新行号为当前位置
                size_data['size_number'] = idx
                
                print(f"插入尺寸数据 {idx}: {size_data}")
                size_query = "INSERT INTO material_sample_size_data (" + ", ".join(['form_id', 'size_number'] + size_fields) + ") VALUES (" + ", ".join(["%s"] * (len(size_fields) + 2)) + ")"
                cursor.execute(size_query, [form_id, idx] + [size_data[field] for field in size_fields])

            # 3. 插入外观检查数据
            print("准备插入外观检查数据...")
            for i in range(1, 5):
                appearance_data = {
                    'form_id': form_id,
                    'check_number': i,
                    'check_result': data.get(f'appearance_{i}_check'),
                    'note': data.get(f'appearance_{i}_note'),
                    'other_info': data.get(f'appearance_{i}_other') if i == 4 else None
                }
                
                if any(appearance_data.values()):  # 只有当有数据时才插入
                    print(f"插入外观检查数据 {i}: {appearance_data}")
                    appearance_query = "INSERT INTO material_sample_appearance (form_id, check_number, check_result, note, other_info) VALUES (%s, %s, %s, %s, %s)"
                    cursor.execute(appearance_query, [form_id, i, appearance_data['check_result'], appearance_data['note'], appearance_data['other_info']])

            # 4. 插入功能检查数据
            print("准备插入功能检查数据...")
            for i in range(1, 7):
                function_data = {
                    'form_id': form_id,
                    'check_number': i,
                    'check_result': data.get(f'function_{i}_check'),
                    'note': data.get(f'function_{i}_note'),
                    'burnin_info': data.get(f'function_{i}_burnin') if i == 4 else None,
                    'electrical_info': data.get(f'function_{i}_electrical') if i == 4 else None,
                    'tests_info': json.dumps(data.get('function_5_tests', []), ensure_ascii=False) if i == 5 else None,
                    'other_test': data.get(f'function_{i}_other_test') if i == 5 else None,
                    'other_info': data.get(f'function_{i}_other') if i == 6 else None
                }
                
                if any(function_data.values()):  # 只有当有数据时才插入
                    print(f"插入功能检查数据 {i}: {function_data}")
                    function_query = "INSERT INTO material_sample_function (form_id, check_number, check_result, note, burnin_info, electrical_info, tests_info, other_test, other_info) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)"
                    cursor.execute(function_query, [
                        form_id, i, function_data['check_result'], function_data['note'],
                        function_data['burnin_info'], function_data['electrical_info'],
                        function_data['tests_info'], function_data['other_test'],
                        function_data['other_info']
                    ])

            # 5. 插入问题记录
            print("准备插入问题记录...")
            
            # 验证图片路径与问题点对应关系
            print(f"[问题点处理] 验证图片路径与问题点对应关系，共{len(temp_ids)}个临时图片ID，{len(image_paths)}个最终图片路径")
            if len(temp_ids) != len(image_paths):
                print(f"[警告] 临时图片数量({len(temp_ids)})与最终路径数量({len(image_paths)})不匹配")
            
            # 创建问题点与图片路径的映射关系
            question_image_map = {}
            for idx, path in enumerate(image_paths, 1):
                # 提取文件名中的问题点序号
                filename = os.path.basename(path)
                try:
                    question_num = int(filename.split('_')[0])
                    question_image_map[question_num] = path
                    print(f"[映射关系] 问题点{question_num} -> 图片路径: {path}")
                except (IndexError, ValueError):
                    question_image_map[idx] = path
                    print(f"[警告] 无法从文件名提取问题点序号，使用默认映射: 问题点{idx} -> 图片路径: {path}")
            
            for i in range(1, 19):  # 支持最多18个问题点
                question_text = data.get(f'question_{i}')
                
                # 从映射关系中获取图片路径
                image_path = question_image_map.get(i)
                
                if image_path:
                    print(f"[问题点{i}] 匹配到图片路径: {image_path}")
                    # 检查图片路径是否包含正确的问题点序号
                    filename = os.path.basename(image_path)
                    try:
                        path_question_num = int(filename.split('_')[0])
                        if path_question_num != i:
                            print(f"[警告] 问题点{i}的图片路径序号不匹配: 路径中的序号为{path_question_num}")
                    except (IndexError, ValueError):
                        print(f"[警告] 无法从图片路径中提取问题点序号: {filename}")
                else:
                    print(f"[问题点{i}] 无匹配图片路径")
                
                # 当存在图片但文本框为空时，自动补上'/'符号
                if image_path and (not question_text or question_text.strip() == ''):
                    question_text = '/'
                    print(f"[问题点{i}] 存在图片但无文本，已自动补上'/'符号")
                
                # 当有文本但无图片时，写入'无图片'代替null
                if question_text and not image_path:
                    image_path = '无图片'
                    print(f"[问题点{i}] 有文本但无图片，已写入'无图片'")
                
                # 验证问题点数据完整性
                if question_text or image_path:
                    print(f"[问题点{i}] 处理详情 - 文本: '{question_text}', 图片: '{image_path}'")
                    question_query = "INSERT INTO material_sample_questions (form_id, question_number, question_text, image_path) VALUES (%s, %s, %s, %s)"
                    cursor.execute(question_query, [form_id, i, question_text, image_path])
                else:
                    print(f"[问题点{i}] 跳过空问题点")

            # 提交事务
            print("提交事务...")
            conn.commit()
            
            # 提交完成后清理UUID映射
            material_number = data.get('material_number')
            if material_number in current_material_uuid:
                del current_material_uuid[material_number]
                print(f"Cleared UUID mapping after submission for material number: {material_number}")
            
            # 返回成功响应
            print(f"表单提交成功，报告编码: {new_code}")
            return jsonify({
                'status': 'success', 
                'message': '数据提交成功！', 
                'report_code': new_code
            })

        except mysql.connector.Error as err:
            print(f"MySQL错误: {err}")
            conn.rollback()
            return jsonify({
                'status': 'error',
                'message': f'数据库错误: {str(err)}'
            }), 500
        except Exception as e:
            print(f"其他错误: {e}")
            conn.rollback()
            return jsonify({
                'status': 'error',
                'message': f'提交失败: {str(e)}'
            }), 500
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 获取临时文件基础路径

def get_temp_base_path(material_number=None):
    try:
        # 读取配置文件获取基础路径
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'settings.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                base_path = config.get('image_base_path', 'D:/检验系统图片/')
        else:
            base_path = 'D:/检验系统图片/'  # 默认路径
            
        # 临时文件夹路径
        temp_base = os.path.join(base_path, 'temp_uploads')
        
        if material_number:
            timestamp = datetime.now().strftime('%Y%m%d')
            
            if material_number in current_material_uuid:
                unique_id = current_material_uuid[material_number]
            else:
                unique_id = str(uuid.uuid4())[:4]
                current_material_uuid[material_number] = unique_id
                
            final_path = os.path.join(temp_base, f"{material_number}_{timestamp}_{unique_id}")
        else:
            final_path = temp_base
            
        os.makedirs(final_path, exist_ok=True)
        return final_path
        
    except Exception as e:
        print(f"Error in get_temp_base_path: {e}")
        raise Exception(f"无法创建或访问临时文件路径: {str(e)}")

# 上传图片到临时路径
@Material_Sample_Confirmation_Form_bp.route('/upload_image', methods=['POST'])
def upload_image():
    try:
        # 验证请求是否包含文件
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        material_number = request.form.get('material_number', '').strip()
        cell_id = request.form.get('cell_id', '').strip()  # 单元格标识参数
        if not material_number or not cell_id:
            return jsonify({'error': 'Material number and cell ID are required'}), 400

        image = request.files['image']
        # 验证文件是否有内容
        if image.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if '.' not in image.filename or image.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
            return jsonify({'error': 'Invalid file type'}), 400

        # 生成文件名（添加毫秒和随机数，确保唯一性）
        ext = image.filename.rsplit('.', 1)[1].lower()  # 提取扩展名
        timestamp = datetime.now().strftime('%y%m%d_%H%M%S_%f')  # 时间戳（添加毫秒）
        random_suffix = random.randint(1000, 9999)  # 生成随机4位数
        filename = f"{cell_id}_{timestamp}_{random_suffix}.{ext}"  # 格式：question1_YYMMDD_HHMMSS_MSEC_RAND.ext

        # 获取临时路径
        temp_subdir = get_temp_base_path(material_number)
        temp_path = os.path.join(temp_subdir, filename)

        # 确保目录存在
        with file_upload_lock:  # 使用锁防止并发冲突
            os.makedirs(temp_subdir, exist_ok=True)

            # 保存文件（移除文件存在检查，因为新的文件名生成逻辑确保了唯一性）
            image.save(temp_path)
            print(f"图片已成功保存到临时路径: {temp_path}")

        return jsonify({
            'success': True,
            'temp_id': cell_id,  # 使用 cell_id 作为临时标识
            'cell_id': cell_id,
            'temp_path': temp_path,
            'message': 'Image uploaded successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

    

    
@Material_Sample_Confirmation_Form_bp.route('/cancel_upload', methods=['GET'])
def cancel_upload():
    temp_id = request.args.get('temp_id')
    material_number = request.args.get('material_number')  # 新增参数获取
    if not temp_id or not material_number:
        return jsonify({'error': 'Missing temp_id or material_number'}), 400

    temp_subdir = get_temp_base_path(material_number)
    temp_path = os.path.join(temp_subdir, temp_id)
    if os.path.exists(temp_path):
        os.remove(temp_path)
        return jsonify({'success': True})
    return jsonify({'error': 'File not found'}), 404

def validate_fields(cursor, table_name, fields):
    cursor.execute(f"DESCRIBE {table_name}")
    db_fields = {row[0] for row in cursor.fetchall()}
    invalid_fields = set(fields) - db_fields
    if invalid_fields:
        raise ValueError(f"Invalid fields: {', '.join(invalid_fields)}")

def move_images(temp_ids, report_code, material_number):
    """
    将临时图片移动到最终存储位置
    返回格式: {"success": bool, "moved_files": list, "final_paths": dict}
    其中final_paths是字典，键是temp_id，值是最终文件路径
    """
    try:
        if not temp_ids:
            print("无图片需要移动")
            return {"success": True, "moved_files": [], "final_paths": {}}
            
        final_path = get_image_base_path(report_code)
        temp_subdir = get_temp_base_path(material_number)
        
        print(f"准备移动图片，temp_ids: {temp_ids}")
        print(f"临时目录: {temp_subdir}")
        print(f"最终目录: {final_path}")
        
        # 列出临时目录中的所有文件
        if os.path.exists(temp_subdir):
            all_files = os.listdir(temp_subdir)
            print(f"临时目录中的所有文件: {all_files}")
        else:
            print(f"临时目录不存在: {temp_subdir}")
            return {"error": f"临时目录不存在: {temp_subdir}"}

        moved_files = []
        final_paths = {}  # 改为字典，键是temp_id，值是最终路径
        for temp_id in temp_ids:
            files_in_temp = os.listdir(temp_subdir)
            valid_files = [f for f in files_in_temp if f.startswith(f"{temp_id}_")]

            print(f"查找以 {temp_id}_ 开头的文件，找到: {valid_files}")

            if not valid_files:
                print(f"警告: 未找到以 {temp_id}_ 开头的文件")
                continue  # 跳过此temp_id，继续处理其他图片

            # 取第一个匹配文件
            temp_filename = valid_files[0]
            temp_path = os.path.join(temp_subdir, temp_filename)

            # 目标文件路径
            final_file_path = os.path.join(final_path, temp_filename)

            # 检查文件存在性
            if not os.path.exists(temp_path):
                print(f"警告: 源文件不存在: {temp_path}")
                continue  # 跳过此文件，继续处理其他图片

            # 移动文件
            shutil.move(temp_path, final_file_path)
            moved_files.append(temp_filename)
            final_paths[temp_id] = final_file_path  # 使用temp_id作为键
            print(f"移动成功: {temp_filename} → {final_file_path}")

        if not moved_files and temp_ids:
            print("警告: 没有成功移动任何文件")
            return {"warning": "没有找到匹配的图片文件，请确认临时图片是否已上传"}
            
        return {"success": True, "moved_files": moved_files, "final_paths": final_paths}
    except Exception as e:
        print(f"移动图片时出错: {str(e)}")
        return {"error": str(e)}

@Material_Sample_Confirmation_Form_bp.route('/confirm_images', methods=['POST'])
def confirm_images_route():
    temp_ids = request.form.getlist('temp_ids[]')
    report_code = request.form['report_code']
    material_number = request.form['material_number']
    return jsonify(move_images(temp_ids, report_code, material_number))

@Material_Sample_Confirmation_Form_bp.route('/debug_images/<report_code>')
def debug_images(report_code):
    """调试路由：检查数据库中的图片路径"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 查询问题点记录
        cursor.execute("""
            SELECT msq.question_number, msq.question_text, msq.image_path, mscf.report_code
            FROM material_sample_questions msq
            JOIN material_sample_confirmation_form mscf ON msq.form_id = mscf.id
            WHERE mscf.report_code = %s
            ORDER BY msq.question_number
        """, (report_code,))

        records = cursor.fetchall()
        result = []

        for record in records:
            image_path = record["image_path"]
            file_exists = False
            if image_path:
                file_exists = os.path.exists(image_path)

            result.append({
                'question_number': record["question_number"],
                'question_text': record["question_text"],
                'image_path': image_path,
                'file_exists': file_exists
            })

        cursor.close()
        conn.close()

        return jsonify({
            'report_code': report_code,
            'records': result,
            'total': len(result)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@Material_Sample_Confirmation_Form_bp.route('/delete_image', methods=['POST'])
def delete_image():
    try:
        data = request.get_json()
        question_id = data.get('question_id')
        material_number = data.get('material_number')

        if not question_id or not material_number:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400

        # 获取临时文件路径
        temp_subdir = get_temp_base_path(material_number)
        print(f"尝试在 {temp_subdir} 中删除以 {question_id}_ 开头的图片")
        
        # 查找并删除对应的图片文件
        files_deleted = False
        if os.path.exists(temp_subdir):
            files_in_temp = os.listdir(temp_subdir)
            print(f"目录中的所有文件: {files_in_temp}")
            
            matching_files = [f for f in files_in_temp if f.startswith(f"{question_id}_")]
            print(f"匹配的文件: {matching_files}")
            
            if matching_files:
                for filename in matching_files:
                    file_path = os.path.join(temp_subdir, filename)
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        print(f"已删除图片: {file_path}")
                        files_deleted = True
        
        # 即使没有找到文件，也返回成功状态，表示图片不在临时目录中
        # 这种情况可能是图片只存在于前端预览，但从未上传到服务器
        if files_deleted:
            return jsonify({'success': True, 'message': '图片删除成功'})
        else:
            return jsonify({'success': True, 'message': '未找到图片，可能已被删除或未上传'})

    except Exception as e:
        print(f"删除图片时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'删除图片失败: {str(e)}'}), 500

@Material_Sample_Confirmation_Form_bp.route('/add_size_row', methods=['POST'])
def add_size_row():
    try:
        # 添加延迟，确保UI有足够时间响应
        time.sleep(0.3)  # 300ms延迟
        
        # 获取当前行数
        current_rows = request.form.get('current_rows', 0, type=int)
        new_row = current_rows + 1
        
        # 检查是否超过最大行数限制
        if new_row > 30:  # 假设最大30行
            return jsonify({
                'status': 'error',
                'message': '已达到最大行数限制'
            })
        
        # 返回新行的HTML
        return jsonify({
            'status': 'success',
            'html': render_template('material_confirmation/size_row.html', row_number=new_row)
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@Material_Sample_Confirmation_Form_bp.route('/remove_size_row', methods=['POST'])
def remove_size_row():
    try:
        # 添加延迟
        time.sleep(0.2)  # 200ms延迟
        
        row_number = request.form.get('row_number', type=int)
        if not row_number:
            return jsonify({
                'status': 'error',
                'message': '无效的行号'
            })
        
        return jsonify({
            'status': 'success',
            'message': '行已删除'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# 添加一个重置物料料号UUID映射的函数
@Material_Sample_Confirmation_Form_bp.route('/reset_material_uuid', methods=['POST'])
def reset_material_uuid():
    try:
        data = request.get_json()
        material_number = data.get('material_number')
        
        if material_number and material_number in current_material_uuid:
            # 从映射中删除该物料料号
            del current_material_uuid[material_number]
            return jsonify({'success': True, 'message': f'已重置物料料号 {material_number} 的临时编码'})
        else:
            # 如果物料料号不存在于映射中，也返回成功
            return jsonify({'success': True, 'message': '无需重置临时编码'})
    except Exception as e:
        print(f"重置物料料号UUID时出错: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

